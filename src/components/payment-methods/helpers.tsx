import type { PaymentMethod } from "./types";
import { NON_DIGIT_REGEX, CREDIT_CARD_FORMAT_REGEX } from "@/constants/regex";
import {
  type BasePaymentMethod,
  PaymentMethodType,
  type CreditDebitCard,
} from "@/types/payment-methods";

export const formatCreditCard = (value: string): string => {
  const cleanValue = value.replace(NON_DIGIT_REGEX, "");
  const formattedValue = cleanValue.replace(CREDIT_CARD_FORMAT_REGEX, "$1 ");
  return formattedValue;
};

export const getPaymentMethodIcon = (
  method: BasePaymentMethod | PaymentMethod,
  t: (key: string) => string,
) => {
  switch (method.type) {
    case PaymentMethodType.CREDIT_CARD:
    case PaymentMethodType.DEBIT_CARD:
    case "credit" as string: {
      const cardMethod = method as CreditDebitCard;
      // Simplified: directly use the brand from API response
      return (
        <img
          src={`src/assets/images/payments/${cardMethod.cardBrand}.png`}
          alt={t(`payment.methods.altText.${cardMethod.cardBrand}`)}
          className="h-8 w-8 object-contain"
        />
      );
    }
    case PaymentMethodType.TRUEMONEY:
    case "truemoney" as string:
      return (
        <img
          src="src/assets/images/payments/truemoney.png"
          alt={t("payment.methods.altText.truemoney")}
          className="h-8 w-8 object-contain"
        />
      );
    case PaymentMethodType.ALIPAY:
    case "alipay" as string:
      return (
        <img
          src="src/assets/images/payments/alipay.png"
          alt={t("payment.methods.altText.alipay")}
          className="h-5 w-5 object-contain"
        />
      );
    case PaymentMethodType.PROMPTPAY:
      return (
        <img
          src="src/assets/images/payments/promptpay.png"
          alt={t("payment.methods.altText.promptpay")}
          className="h-8 w-8 object-contain"
        />
      );
    case PaymentMethodType.MOBILE_BANKING:
      return (
        <img
          src="src/assets/images/payments/mobile-banking.png"
          alt={t("payment.methods.altText.mobileBanking")}
          className="h-8 w-8 object-contain"
        />
      );
    default:
      return (
        <img
          src="src/assets/images/payments/mastercard.png"
          alt={t("payment.methods.altText.creditCard")}
          className="h-10 w-10 object-contain"
        />
      );
  }
};

export const getPaymentMethodName = (
  type: PaymentMethodType | PaymentMethod["type"],
  t: (key: string) => string,
) => {
  switch (type) {
    case PaymentMethodType.CREDIT_CARD:
    case "credit" as string:
      return t("payment.methods.types.credit");
    case PaymentMethodType.DEBIT_CARD:
      return t("payment.methods.types.debit");
    case PaymentMethodType.TRUEMONEY:
    case "truemoney" as string:
      return t("payment.methods.types.truemoney");
    case PaymentMethodType.ALIPAY:
    case "alipay" as string:
      return t("payment.methods.types.alipay");
    case PaymentMethodType.PROMPTPAY:
      return t("payment.methods.types.promptpay");
    case PaymentMethodType.MOBILE_BANKING:
      return t("payment.methods.types.mobileBanking");
    default:
      return "";
  }
};
