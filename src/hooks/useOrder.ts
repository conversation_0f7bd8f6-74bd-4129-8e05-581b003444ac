import { useState } from "react";
import { orderService } from "@/services/order/order-service";
import type { 
  CreateOrderRequest, 
  CreateOrderResponse,
  Cart,
  ShippingMethodData,
  PaymentMethodData
} from "@/types/order";
import type { CheckoutFormData } from "@/pages/checkout/constants";

export const useOrder = () => {
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [orderError, setOrderError] = useState<string | null>(null);
  
  const createOrderFromCheckout = async (
    formData: CheckoutFormData,
    cart: Cart,
    shippingMethodString: string,
    paymentMethodString: string,
    cardProfileId?: number,
    token?: string,
    deliveryAddressId: number = 0
  ): Promise<CreateOrderResponse | null> => {
    setIsCreatingOrder(true);
    setOrderError(null);

    try {
      // Parse shipping method
      const shippingMethod = orderService.parseShippingMethod(shippingMethodString);
      
      // Parse payment method
      const paymentMethod = orderService.parsePaymentMethod(
        paymentMethodString, 
        cardProfileId, 
        token
      );

      // Map form data to order request
      const orderRequest = orderService.mapCheckoutDataToOrderRequest(
        formData,
        cart,
        shippingMethod,
        paymentMethod,
        deliveryAddressId
      );

      // Create the order
      const response = await orderService.createOrder(orderRequest);
      
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create order";
      setOrderError(errorMessage);
      console.error("Order creation error:", error);
      return null;
    } finally {
      setIsCreatingOrder(false);
    }
  };

  /**
   * Creates an order directly from order request data
   */
  const createOrder = async (orderData: CreateOrderRequest): Promise<CreateOrderResponse | null> => {
    setIsCreatingOrder(true);
    setOrderError(null);

    try {
      const response = await orderService.createOrder(orderData);
      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to create order";
      setOrderError(errorMessage);
      console.error("Order creation error:", error);
      return null;
    } finally {
      setIsCreatingOrder(false);
    }
  };

  /**
   * Clears any existing order error
   */
  const clearOrderError = () => {
    setOrderError(null);
  };

  return {
    isCreatingOrder,
    orderError,
    createOrderFromCheckout,
    createOrder,
    clearOrderError,
  };
};
