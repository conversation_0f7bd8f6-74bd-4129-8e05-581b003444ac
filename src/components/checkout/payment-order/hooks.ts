import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { checkoutSchema } from "@/pages/checkout/constants";
import type { CheckoutFormData } from "@/pages/checkout/constants";

export const usePaymentForm = () => {
  return useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      paymentMethod: "",
    },
  });
};
