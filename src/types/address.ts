import { z } from "zod";

export interface Address {
  id: number;
  is_default: boolean;
  address_line1: string;
  address_line2: string;
  sub_district: string;
  district: string;
  city: string;
  province: string;
  postal_code: string;
  country: string;
  full_address: string;
  created_at: string;
  updated_at: string;
}

export interface AddressListResponse {
  results: Address[];
  count: number;
  next?: string;
  previous?: string;
}

export interface AddressFormData {
  address_line1: string;
  address_line2?: string;
  sub_district: string;
  district: string;
  city: string;
  province: string;
  postal_code: string;
  country: string;
  delivery_instructions?: string;
  is_default?: boolean;
}

export const addressSchema = z.object({
  // recipient_name: z
  //   .string()
  //   .min(2, "ชื่อผู้รับต้องมีอย่างน้อย 2 ตัวอักษร")
  //   .max(100, "ชื่อผู้รับต้องไม่เกิน 100 ตัวอักษร"),
  // phone: z
  //   .string()
  //   .min(10, "หมายเลขโทรศัพท์ไม่ถูกต้อง")
  //   .max(15, "หมายเลขโทรศัพท์ไม่ถูกต้อง"),
  address_line1: z
    .string()
    .min(5, "ที่อยู่ต้องมีอย่างน้อย 5 ตัวอักษร")
    .max(200, "ที่อยู่ต้องไม่เกิน 200 ตัวอักษร"),
  address_line2: z
    .string()
    .max(200, "ที่อยู่ต้องไม่เกิน 200 ตัวอักษร")
    .optional(),
  sub_district: z
    .string()
    .min(2, "ตำบล/แขวงต้องมีอย่างน้อย 2 ตัวอักษร")
    .max(100, "ตำบล/แขวงต้องไม่เกิน 100 ตัวอักษร"),
  district: z
    .string()
    .min(2, "อำเภอ/เขตต้องมีอย่างน้อย 2 ตัวอักษร")
    .max(100, "อำเภอ/เขตต้องไม่เกิน 100 ตัวอักษร"),
  city: z
    .string()
    .min(2, "เมืองต้องมีอย่างน้อย 2 ตัวอักษร")
    .max(100, "เมืองต้องไม่เกิน 100 ตัวอักษร"),
  province: z
    .string()
    .min(2, "จังหวัดต้องมีอย่างน้อย 2 ตัวอักษร")
    .max(100, "จังหวัดต้องไม่เกิน 100 ตัวอักษร"),
  postal_code: z.string().regex(/^\d{5}$/, "รหัสไปรษณีย์ต้องเป็นตัวเลข 5 หลัก"),
  country: z
    .string()
    .min(2, "ประเทศต้องมีอย่างน้อย 2 ตัวอักษร")
    .max(100, "ประเทศต้องไม่เกิน 100 ตัวอักษร"),
  is_default: z.boolean().default(false),
});

export type AddressFormDataValidated = z.infer<typeof addressSchema>;
