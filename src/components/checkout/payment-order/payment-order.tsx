import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@base/button";
import { Badge } from "@commons/badge";
import { ArrowLeft } from "lucide-react";
import { PaymentMethod } from "@/components/checkout";
import { checkoutSchema } from "@/pages/checkout/constants";
import type { CheckoutFormData } from "@/pages/checkout/constants";
import type { PaymentOrderProps } from "./types";
import { formatPrice, formatOrderDate } from "./helpers";

export const PaymentOrder: React.FC<PaymentOrderProps> = ({
  order,
  onBackToCreateOrder,
  onPaymentComplete,
}) => {
  const { isProcessingPayment, paymentStatus, processPayment } = usePaymentProcessing(onPaymentComplete);

  const StatusIcon = getStatusIcon(paymentStatus);
  const statusColor = getStatusColor(paymentStatus);
  const statusText = getStatusText(paymentStatus);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBackToCreateOrder}
          disabled={isProcessingPayment || paymentStatus === PAYMENT_STATUS.PAID}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          กลับไปแก้ไข
        </Button>
        <h1 className="text-[32px] font-medium">ชำระเงิน</h1>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold">คำสั่งซื้อ #{order.id}</h2>
          <Badge variant="outline" className="text-xs">
            สร้างเมื่อ {formatOrderDate(order.created_at)}
          </Badge>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center py-2 border-b">
            <span className="font-medium">ยอดรวม</span>
            <span className="text-lg font-semibold text-primary">
              {formatPrice(order.total_price)}
            </span>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium text-sm text-gray-600">รายการสินค้า</h3>
            {order.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center text-sm">
                <div>
                  <span className="font-medium">{item.sku}</span>
                  <span className="text-gray-500 ml-2">x{item.quantity}</span>
                </div>
                <span>{formatPrice(item.price)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-4">
          <StatusIcon className={`h-5 w-5 ${statusColor}`} />
          <h3 className="text-lg font-semibold">{statusText}</h3>
        </div>

        {paymentStatus === PAYMENT_STATUS.PENDING && (
          <div className="space-y-4">
            <p className="text-gray-600">
              กรุณาตรวจสอบข้อมูลคำสั่งซื้อและดำเนินการชำระเงิน
            </p>
            <Button
              onClick={processPayment}
              size="lg"
              className="w-full"
              disabled={isProcessingPayment}
            >
              ชำระเงิน {formatPrice(order.total_price)}
            </Button>
          </div>
        )}

        {paymentStatus === PAYMENT_STATUS.PAID && (
          <div className="text-center py-8">
            <div className="text-green-600">
              <CheckCircle className="h-16 w-16 mx-auto mb-4" />
              <p className="text-lg font-semibold">การชำระเงินสำเร็จ!</p>
              <p className="text-sm text-gray-600 mt-2">
                คำสั่งซื้อของคุณได้รับการยืนยันแล้ว
              </p>
            </div>
          </div>
        )}

        {paymentStatus === PAYMENT_STATUS.FAILED && (
          <div className="space-y-4">
            <div className="text-center text-red-600">
              <p>การชำระเงินล้มเหลว กรุณาลองใหม่อีกครั้ง</p>
            </div>
            <Button
              onClick={processPayment}
              size="lg"
              className="w-full"
              variant="outline"
            >
              ลองชำระเงินอีกครั้ง
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
