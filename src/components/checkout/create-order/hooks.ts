import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { checkoutSchema } from "@/pages/checkout/constants";
import type { CheckoutFormData } from "@/pages/checkout/constants";
import { getFormDefaultValues } from "./helpers";

export const useCreateOrderForm = (userEmail?: string) => {
  const form = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: getFormDefaultValues(userEmail),
  });

  React.useEffect(() => {
    if (userEmail) {
      form.setValue("contactInfo.email", userEmail);
      form.setValue("taxInvoice.personalInfo.email", userEmail);
    }
  }, [userEmail, form]);

  return form;
};
