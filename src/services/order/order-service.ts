import type { CheckoutFormData } from "@/pages/checkout/constants";
import { ApiService } from "@/services/api";
import { ORDER_ENDPOINTS } from "@/services/api/constants";
import type {
  Cart,
  CreateOrderRequest,
  CreateOrderResponse,
  PaymentMethodData,
  ShippingMethodData,
} from "@/types/order";

class OrderService {
  mapCheckoutDataToOrderRequest(
    formData: CheckoutFormData,
    cart: Cart,
    shippingMethod: ShippingMethodData,
    paymentMethod: PaymentMethodData,
    deliveryAddressId: number = 0,
  ): CreateOrderRequest {
    const { shippingAddress, taxInvoice, contactInfo } = formData;

    // Extract recipient name from shipping address
    const recipientName =
      `${shippingAddress.firstName} ${shippingAddress.lastName}`.trim();

    // Determine tax information
    const useTax = taxInvoice.wantTaxInvoice;
    const taxType =
      taxInvoice.personalInfo?.type === "company" ? "company" : "personal";

    // Use delivery address for tax if specified
    const useDeliveryAddress = taxInvoice.taxInfo?.useSameAddress ?? true;

    const orderRequest: CreateOrderRequest = {
      // Basic order info
      recipient_name: recipientName,
      cart_id: cart.id,

      // Delivery information
      delivery_provider: shippingMethod.provider,
      delivery_type: shippingMethod.type,
      delivery_address_id: deliveryAddressId,

      // Contact information
      phone: taxInvoice.personalInfo?.phone || "",

      // Shipping address
      address_line1: shippingAddress.address_line1,
      address_line2: shippingAddress.address_line2 || "",
      sub_district: shippingAddress.subdistrict,
      district: shippingAddress.district,
      city: shippingAddress.city,
      province: shippingAddress.province,
      postal_code: shippingAddress.postal_code,
      country: "Thailand",

      // Payment information
      payment_method: paymentMethod.method,
      card_profile_id: paymentMethod.card_profile_id || 0,
      token: paymentMethod.token || "",

      // Tax information
      use_tax: useTax,
      tax_type: useTax ? taxType : undefined,
      use_delivery_address: useDeliveryAddress,

      // Personal tax info (for individual)
      tax_first_name:
        useTax && taxType === "personal"
          ? taxInvoice.personalInfo?.firstName
          : undefined,
      tax_last_name:
        useTax && taxType === "personal"
          ? taxInvoice.personalInfo?.lastName
          : undefined,

      // Company tax info (for company)
      business_tax_id:
        useTax && taxType === "company" ? taxInvoice.taxInfo?.taxId : undefined,
      company_name:
        useTax && taxType === "company"
          ? taxInvoice.personalInfo?.companyName
          : undefined,

      // Tax contact info
      tax_phone: useTax ? taxInvoice.personalInfo?.phone : undefined,
      tax_email: useTax ? taxInvoice.personalInfo?.email : undefined,

      // Tax address (if different from delivery address)
      tax_address_line1:
        useTax && !useDeliveryAddress
          ? taxInvoice.taxInfo?.address_line1
          : undefined,
      tax_address_line2:
        useTax && !useDeliveryAddress
          ? taxInvoice.taxInfo?.address_line2
          : undefined,
      tax_sub_district:
        useTax && !useDeliveryAddress
          ? taxInvoice.taxInfo?.subdistrict
          : undefined,
      tax_district:
        useTax && !useDeliveryAddress
          ? taxInvoice.taxInfo?.district
          : undefined,
      tax_city:
        useTax && !useDeliveryAddress ? taxInvoice.taxInfo?.city : undefined,
      tax_province:
        useTax && !useDeliveryAddress
          ? taxInvoice.taxInfo?.province
          : undefined,
      tax_postal_code:
        useTax && !useDeliveryAddress
          ? taxInvoice.taxInfo?.postal_code
          : undefined,
      tax_country: useTax && !useDeliveryAddress ? "Thailand" : undefined,
    };

    return orderRequest;
  }

  async createOrder(
    orderData: CreateOrderRequest,
  ): Promise<CreateOrderResponse> {
    try {
      const response = await ApiService.post<CreateOrderResponse>(
        ORDER_ENDPOINTS.CREATE,
        orderData,
      );
      return response;
    } catch (error) {
      throw new Error(`Failed to create order: ${(error as Error).message}`);
    }
  }

  parseShippingMethod(shippingMethodString: string): ShippingMethodData {
    const [provider, type] = shippingMethodString.split("_");

    return {
      provider: provider as "shopee" | "flash",
      type: type as "1" | "2" | "3",
    };
  }

  parsePaymentMethod(
    paymentMethodString: string,
    cardProfileId?: number,
    token?: string,
  ): PaymentMethodData {
    return {
      method: paymentMethodString,
      card_profile_id: cardProfileId,
      token: token,
    };
  }
}

export const orderService = new OrderService();
