import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@base/card.tsx";
import React, { useState } from "react";
import { useTranslation } from "@hooks/useTranslation.ts";
import { Badge } from "@/components/commons/badge";
import { Spinner } from "@/components/commons/loading-spinner";
import { SquarePen } from "lucide-react";
import { useAddresses } from "@/hooks/useAddresses";
import type { Address as AddressType, AddressFormData } from "@/types/address";
import { AddressForm } from "./address-form";
import { AddAddressButton } from "./add-address-button";
import type { AddressItemProps } from "@components/address/types.ts";

const AddressItem: React.FC<AddressItemProps> = ({ address, onEdit, t }) => {
  return (
    <div className="border-primary-border-card mb-3 w-full rounded-[16px] border p-4">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <p className="mb-1 text-sm text-gray-600">{address.phone}</p>
          <p className="text-sm text-gray-700">{address.full_address}</p>
        </div>
        <button
          onClick={() => onEdit(address)}
          className="ml-4 p-2 transition-colors hover:text-gray-600"
          aria-label={t("address.edit")}
        >
          <SquarePen className="h-4 w-4" />
        </button>
      </div>
    </div>
  );
};

export const Address: React.FC = () => {
  const { t } = useTranslation();
  const { addresses, isLoading, error, addAddress, updateAddress } =
    useAddresses();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<AddressType | null>(
    null,
  );

  const handleAddAddress = () => {
    setEditingAddress(null);
    setIsFormOpen(true);
  };

  const handleEditAddress = (address: AddressType) => {
    setEditingAddress(address);
    setIsFormOpen(true);
  };

  const handleFormSubmit = async (data: AddressFormData) => {
    try {
      if (editingAddress) {
        // Update existing address
        await updateAddress(editingAddress.id, data);
      } else {
        // Add new address
        await addAddress(data);
      }
    } catch (error) {
      console.error("Error saving address:", error);
      throw error; // Re-throw to let form handle the error
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setEditingAddress(null);
  };

  if (isLoading) {
    return (
      <Card className="md:border-primary-border-card w-full sm:border-none md:border">
        <CardHeader>
          <CardTitle className="font-regular text-[23px] text-black">
            {t("address.title")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-8">
            <Spinner />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="md:border-primary-border-card w-full sm:border-none md:border">
        <CardHeader>
          <CardTitle className="font-regular text-[23px] text-black">
            {t("address.title")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="py-8 text-center">
            <p className="text-red-600">{t("address.error")}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="md:border-primary-border-card w-full sm:border-none md:border">
      <CardHeader>
        <CardTitle className="font-regular text-[23px] text-black">
          {t("address.title")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="lg:w-1/2">
          {addresses.length === 0 ? (
            <div className="py-8 text-center">
              <p className="text-gray-500">{t("address.noAddresses")}</p>
            </div>
          ) : (
            <div className="space-y-3">
              {addresses.map((address) => (
                <AddressItem
                  key={address.id}
                  address={address}
                  onEdit={handleEditAddress}
                  t={t}
                />
              ))}
            </div>
          )}
          <AddAddressButton onClick={handleAddAddress} />
        </div>

        {/* Address Form Dialog */}
        <AddressForm
          isOpen={isFormOpen}
          onClose={handleFormClose}
          onSubmit={handleFormSubmit}
          initialData={editingAddress || undefined}
          isEdit={!!editingAddress}
        />
      </CardContent>
    </Card>
  );
};

export default Address;
