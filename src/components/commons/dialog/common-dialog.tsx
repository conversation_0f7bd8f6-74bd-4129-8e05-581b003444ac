import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/commons/base/dialog";
import { Button } from "@/components/commons/base/button";
import { cn } from "@/lib/utils";
import type { CommonDialogProps, CancelDialogProps } from "./types";

export function CommonDialog({
  open,
  onOpenChange,
  title,
  description,
  children,
  footer,
  showCloseButton = true,
  className,
  trigger,
}: CommonDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {trigger && <DialogTrigger asChild>{trigger}</DialogTrigger>}
      <DialogContent
        showCloseButton={showCloseButton}
        className={cn(
          "w-[90%] max-w-[90%] md:w-[600px] rounded-[16px] border-none",
          className,
        )}
      >
        {(title || description) && (
          <DialogHeader>
            {title && <DialogTitle>{title}</DialogTitle>}
            {description && (
              <DialogDescription>
                <span className="text-[length:var(--font-size-md)] font-semibold">
                  {description}
                </span>
              </DialogDescription>
            )}
          </DialogHeader>
        )}
        {children}
        {footer && <DialogFooter>{footer}</DialogFooter>}
      </DialogContent>
    </Dialog>
  );
}

export function CancelDialog({
  onCancel,
  onConfirm,
  cancelText = "ยกเลิก",
  confirmText = "ออกจากระบบ",
  description = "แน่ใจหรือไม่ว่าต้องการออกจากระบบ?",
  ...props
}: CancelDialogProps) {
  return (
    <CommonDialog
      {...props}
      showCloseButton={false}
      description={description}
      footer={
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={onCancel}>
            {cancelText}
          </Button>
          <Button
            variant="destructive"
            className="bg-cancel-button"
            onClick={onConfirm}
          >
            {confirmText}
          </Button>
        </div>
      }
    />
  );
}
