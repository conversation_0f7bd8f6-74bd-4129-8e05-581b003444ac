{"navbar": {"home": "首页", "products": "产品", "login": "登录 / 注册", "myAccount": "我的账户", "cart": "购物车", "overview": "概览", "orders": "订单", "favorites": "收藏", "accountSettings": "账户设置", "logout": "退出登录"}, "home": {"title": "<PERSON><PERSON> Muse 电子商务", "subtitle": "使用 Vite、React、Tailwind CSS 和 shadcn/ui 构建的现代电子商务平台。", "register": "立即注册", "hero": {"title-1": "ช้อปสนุกกับ", "title-2": "ซีรีส์ที่คุณรัก!", "paragraph": "ฟินสุดขีดกับไอเทมหายากจากซีรีส์วายเรื่องโปรด ห้ามพลาด! ทุกชิ้นส่งตรงจาก Mojo Muse รับประกันความฟิน 300%"}, "recommend": {"title": "สินค้าแนะนำ", "all": "ดูสินค้าแนะนำทั้งหมด"}}, "products": {"title": "产品", "subtitle": "浏览我们的产品系列。", "filter": {"title": "ตัวกรอง", "category": "ศิลปิน", "clear": "ล้าง", "use-filter": "ใช้ตัวกรอง"}}, "product-detail": {"isInStock": "In Stock", "outOfStock": "Out of Stock", "size": "Size", "addToCart": "เพิ่มลงตะกร้า", "interest": "คุณอาจสนใจ"}, "login": {"title": "登录", "email": "电子邮件地址", "password": "密码", "rememberMe": "记住我", "forgotPassword": "忘记密码？", "submit": "登录", "submitting": "登录中...", "success": "登录成功！查看控制台获取数据。", "googleSuccess": "Google 登录成功！", "orContinueWith": "或继续使用", "continueWithGoogle": "使用 Google 继续", "noAccount": "没有账户？", "register": "注册"}, "register": {"title": "注册", "updateTitle": "更新资料", "email": "电子邮件地址", "firstName": "名字", "lastName": "姓氏", "password": "密码", "age": "年龄", "phone": "电话号码", "gender": "性别", "nationality": "国籍", "submit": "注册", "submitting": "注册中...", "success": "{name} 注册成功！查看控制台获取数据。", "updateSuccess": "{name} 资料更新成功！查看控制台获取数据。", "validation": {"phone": {"min": "电话号码长度必须至少为10个字符", "max": "电话号码长度不能超过15个字符"}, "gender": {"required": "性别为必填项", "max": "性别长度不能超过50个字符"}}}, "notFound": {"title": "404 - 页面未找到", "message": "您正在寻找的页面不存在。", "goHome": "返回首页"}, "myAccount": {"favoriteSettings": "收藏设置", "overviewTitle": "账户概览", "overviewContent": "您的账户概览信息将显示在这里", "ordersTitle": "我的订单", "ordersContent": "您的订单历史将显示在这里", "favoritesTitle": "我的收藏", "favoritesContent": "您收藏的商品将显示在这里", "favoriteSettingsTitle": "收藏设置", "favoriteSettingsContent": "您的收藏设置将显示在这里"}, "payment": {"methods": {"title": "支付方式", "current": "当前支付方式", "main": "主要支付方式", "add": "添加支付方式", "addCredit": "添加信用卡/借记卡", "edit": "编辑支付方式", "create": "添加支付方式", "cancel": "取消", "save": "保存", "addButton": "添加卡", "setDefault": "设为默认", "lastUsed": "最后使用: {date}", "types": {"credit": "信用卡", "debit": "借记卡", "truemoney": "TrueMoney", "alipay": "支付宝", "promptpay": "PromptPay", "mobileBanking": "移动银行"}, "form": {"type": "支付方式类型", "cardBrand": "卡品牌", "cardNumber": "卡号", "holderName": "持卡人姓名", "expiryDate": "有效期", "expiryMonth": "有效期月份", "expiryYear": "有效期年份", "cvv": "安全码", "email": "邮箱", "account": "账户信息", "phoneNumber": "电话号码", "displayName": "显示名称（可选）", "isDefault": "设为默认支付方式", "placeholders": {"cardNumber": "1234 5678 9012 3456", "holderName": "张三", "expiryDate": "MM/YY", "expiryMonth": "MM", "expiryYear": "YYYY", "cvv": "123", "email": "<EMAIL>", "account": "输入账户信息", "phoneNumber": "**********", "displayName": "我的卡"}, "validation": {"cardNumberRequired": "卡号为必填项", "cardNumberFormat": "卡号必须为16位数字", "cardNumberInvalid": "卡号无效", "holderNameRequired": "持卡人姓名为必填项", "expiryDateRequired": "有效期为必填项", "expiryDateFormat": "有效期必须为MM/YY格式", "expiryMonthRequired": "有效期月份为必填项", "expiryYearRequired": "有效期年份为必填项", "expiryInvalid": "卡已过期", "cvvRequired": "安全码为必填项", "cvvFormat": "安全码必须为3-4位数字", "emailRequired": "邮箱为必填项", "emailFormat": "邮箱格式无效", "accountRequired": "账户信息为必填项", "phoneNumberRequired": "电话号码为必填项", "phoneNumberFormat": "电话号码必须为10位数字", "phoneNumberInvalid": "该电话号码未在TrueMoney注册"}}, "altText": {"visa": "Visa", "mastercard": "Mastercard", "jcb": "JCB", "amex": "American Express", "truemoney": "TrueMoney", "alipay": "支付宝", "promptpay": "PromptPay", "mobileBanking": "移动银行", "creditCard": "信用卡"}, "creditCardDisplay": "信用卡/借记卡 {brand} 尾号 {lastDigits}", "maskedCardNumber": "** {lastDigits}", "directOmise": "直接访问 Omise API", "empty": "未找到支付方式", "delete": "删除支付方式", "deleteConfirmation": "您确定要删除此支付方式吗？", "deleteSuccess": "支付方式删除成功", "deleteError": "删除支付方式失败"}}, "address": {"title": "收货地址", "main": "默认地址", "deliveryInstructions": "配送说明", "edit": "编辑", "error": "加载地址时出错", "noAddresses": "没有保存的地址", "addNew": "添加新地址", "form": {"addTitle": "添加新地址", "editTitle": "编辑地址", "recipientName": "收件人姓名", "recipientNamePlaceholder": "请输入收件人姓名", "phone": "电话号码", "phonePlaceholder": "请输入电话号码", "addressLine1": "地址第一行", "addressLine1Placeholder": "门牌号、街道、路", "addressLine2": "地址第二行", "addressLine2Placeholder": "建筑物、楼层、房间（可选）", "subdistrict": "街道/社区", "subdistrictPlaceholder": "请输入街道/社区", "district": "区/县", "districtPlaceholder": "请输入区/县", "city": "城市", "cityPlaceholder": "请输入城市", "province": "省份", "provincePlaceholder": "请输入省份", "postalCode": "邮政编码", "postalCodePlaceholder": "请输入邮政编码", "country": "国家", "countryPlaceholder": "请输入国家", "deliveryInstructions": "配送说明", "deliveryInstructionsPlaceholder": "额外的配送说明（可选）", "setAsDefault": "设为默认地址", "cancel": "取消", "add": "添加地址", "update": "更新地址", "adding": "添加中...", "updating": "更新中..."}}, "taxInvoice": {"title": "申请完整税务发票", "wantTaxInvoice": "需要税务发票", "noTaxInvoice": "不需要税务发票", "personalInfo": {"title": "基本信息", "type": "类型", "typePlaceholder": "选择类型", "individual": "个人", "company": "公司", "firstName": "名字", "lastName": "姓氏", "companyName": "公司名称", "email": "邮箱", "phone": "电话号码"}, "taxInfo": {"title": "税务信息", "taxId": "税务识别号", "taxIdIndividual": "身份证号码", "taxIdCompany": "企业税务识别号", "useSameAddress": "使用与配送地址相同的地址", "addressLine1": "地址第一行", "addressLine2": "地址第二行", "subdistrict": "街道/社区", "district": "区/县", "city": "城市", "province": "省份", "postalCode": "邮政编码", "country": "国家"}}, "shippingMethod": {"title": "配送方式"}}