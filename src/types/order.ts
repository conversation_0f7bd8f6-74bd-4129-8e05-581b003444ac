// Order API types based on the provided interface

export interface CreateOrderRequest {
  recipient_name: string;
  cart_id: number;
  delivery_provider: "shopee" | "kerry" | "flash" | "j&t";
  delivery_type: "1" | "2" | "3"; // Standard, Express, Same Day
  delivery_address_id: number;
  phone: string;
  address_line1: string;
  address_line2?: string;
  sub_district: string;
  district: string;
  city: string;
  province: string;
  postal_code: string;
  country: "Thailand";
  payment_method: string;
  card_profile_id?: number;
  token?: string;
  use_tax: boolean;
  tax_type?: "personal" | "company";
  tax_first_name?: string;
  tax_last_name?: string;
  use_delivery_address: boolean;
  business_tax_id?: string;
  company_name?: string;
  tax_phone?: string;
  tax_email?: string;
  tax_address_line1?: string;
  tax_address_line2?: string;
  tax_sub_district?: string;
  tax_district?: string;
  tax_city?: string;
  tax_province?: string;
  tax_postal_code?: string;
  tax_country?: "Thailand";
}

export interface OrderItem {
  product_variant: number;
  quantity: number;
  price: string;
  sku: string;
}

export interface CreateOrderResponse {
  id: number;
  user: number;
  total_price: string;
  created_at: string;
  updated_at: string;
  items: OrderItem[];
}

// Helper types for mapping
export interface CartItem {
  id: number;
  product_variant_id: number;
  quantity: number;
  price: number;
  sku: string;
}

export interface Cart {
  id: number;
  items: CartItem[];
  total_price: number;
}

export interface ShippingMethodData {
  provider: "shopee" | "kerry" | "flash" | "j&t";
  type: "1" | "2" | "3";
}

export interface PaymentMethodData {
  method: string;
  card_profile_id?: number;
  token?: string;
}
