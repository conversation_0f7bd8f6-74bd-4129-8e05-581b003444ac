import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@base/button";
import { useAuth } from "@store/useAuthStore";
import { useOrder } from "@/hooks/useOrder";
import { ShoppingCart } from "@/components/shopping-cart";
import {
  ContactInfo,
  ShippingAddress,
  TaxInvoice,
  ShippingMethod,
  PaymentMethod,
  TermsConditions,
} from "@/components/checkout";
import { checkoutSchema } from "./constants";
import type { CheckoutFormData } from "./constants";
import type { Cart } from "@/types/order";

const CheckoutPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      contactInfo: {
        email: user?.email || "",
      },
      taxInvoice: {
        wantTaxInvoice: false,
        personalInfo: {
          type: "individual",
          firstName: "",
          lastName: "",
          email: user?.email || "",
          phone: "",
        },
      },
      agreeToTerms: false,
    },
  });

  useEffect(() => {
    if (user?.email) {
      setValue("contactInfo.email", user.email);
      setValue("taxInvoice.personalInfo.email", user.email);
    }
  }, [user, setValue]);

  const onSubmit = async (data: CheckoutFormData) => {
    try {
      console.log("Checkout data:", data);
      // TODO: Implement checkout API call
      // navigate("/checkout/success");
    } catch (error) {
      console.error("Checkout error:", error);
    }
  };

  return (
    <div className="bg-bg-card py-5 pt-[140px]">
      <div className="mx-auto w-full px-8 pb-8">
        <h1 className="mb-8 text-[32px] font-medium">เช็คเอ้าท์</h1>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Contact Information */}
              <ContactInfo
                control={control}
                errors={errors}
                isAuthenticated={isAuthenticated}
              />

              {/* Shipping Address */}
              <ShippingAddress
                control={control}
                errors={errors}
                setValue={setValue}
              />

              {/* Tax Invoice */}
              <TaxInvoice
                control={control}
                errors={errors}
                watch={watch}
                setValue={setValue}
              />

              {/* Shipping Method */}
              <ShippingMethod control={control} errors={errors} />

              {/* Mobile: Shopping Cart at top */}
              <div className="lg:hidden">
                <ShoppingCart />
              </div>

              {/* Payment Method */}
              <PaymentMethod control={control} errors={errors} watch={watch} />

              {/* Terms and Conditions */}
              <TermsConditions control={control} errors={errors} />

              {/* Submit Button */}
              <Button
                type="submit"
                size="lg"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? "กำลังดำเนินการ..." : "ชำระเงิน"}
              </Button>
            </form>
          </div>

          {/* Desktop: Shopping Cart Sidebar */}
          <div className="hidden lg:block">
            <ShoppingCart />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
