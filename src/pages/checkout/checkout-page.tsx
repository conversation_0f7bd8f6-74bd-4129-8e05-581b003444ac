import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@store/useAuthStore";
import { useOrder } from "@/hooks/useOrder";
import { ShoppingCart } from "@/components/shopping-cart";
import { CreateOrder, PaymentOrder } from "@/components/checkout";
import type { CheckoutFormData } from "./constants";
import type { Cart, CreateOrderResponse } from "@/types/order";

type CheckoutStep = "create-order" | "payment-order";

const CheckoutPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const { isCreatingOrder, orderError, createOrderFromCheckout, clearOrderError } = useOrder();

  const [currentStep, setCurrentStep] = useState<CheckoutStep>("create-order");
  const [createdOrder, setCreatedOrder] = useState<CreateOrderResponse | null>(null);

  const handleCreateOrder = async (data: CheckoutFormData) => {
    try {
      clearOrderError();

      const mockCart: Cart = {
        id: 1,
        items: [
          {
            id: 1,
            product_variant_id: 123,
            quantity: 2,
            price: 1500,
            sku: "PROD-001"
          }
        ],
        total_price: 3000
      };

      const paymentMethodString = data.paymentMethod;
      const cardProfileId = undefined;
      const token = undefined;
      const deliveryAddressId = 0;

      const orderResponse = await createOrderFromCheckout(
        data,
        mockCart,
        data.shippingMethod,
        paymentMethodString,
        cardProfileId,
        token,
        deliveryAddressId
      );

      if (orderResponse) {
        setCreatedOrder(orderResponse);
        setCurrentStep("payment-order");
      }
    } catch (error) {
      console.error("Order creation error:", error);
    }
  };

  const handleBackToCreateOrder = () => {
    setCurrentStep("create-order");
    setCreatedOrder(null);
    clearOrderError();
  };

  const handlePaymentComplete = () => {
    if (createdOrder) {
      navigate("/checkout/success", {
        state: { orderId: createdOrder.id }
      });
    }
  };

  const renderLeftContent = () => {
    switch (currentStep) {
      case "create-order":
        return (
          <CreateOrder
            isAuthenticated={isAuthenticated}
            userEmail={user?.email}
            isCreatingOrder={isCreatingOrder}
            orderError={orderError}
            onCreateOrder={handleCreateOrder}
          />
        );
      case "payment-order":
        return createdOrder ? (
          <PaymentOrder
            order={createdOrder}
            onBackToCreateOrder={handleBackToCreateOrder}
            onPaymentComplete={handlePaymentComplete}
          />
        ) : null;
      default:
        return null;
    }
  };

  return (
    <div className="bg-bg-card py-5 pt-[140px]">
      <div className="mx-auto w-full px-8 pb-8">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          <div className="lg:col-span-2">
            <div className="lg:hidden mb-6">
              <ShoppingCart />
            </div>
            {renderLeftContent()}
          </div>

          <div className="hidden lg:block">
            <ShoppingCart />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
