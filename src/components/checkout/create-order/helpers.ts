import type { CheckoutFormData } from "@/pages/checkout/constants";

export const getFormDefaultValues = (userEmail?: string): Partial<CheckoutFormData> => ({
  contactInfo: {
    email: userEmail || "",
  },
  taxInvoice: {
    wantTaxInvoice: false,
    personalInfo: {
      type: "individual",
      firstName: "",
      lastName: "",
      email: userEmail || "",
      phone: "",
    },
  },
  agreeToTerms: false,
});

export const getButtonText = (isCreatingOrder: boolean, isSubmitting: boolean): string => {
  if (isCreatingOrder) return "กำลังสร้างคำสั่งซื้อ...";
  if (isSubmitting) return "กำลังดำเนินการ...";
  return "สร้างคำสั่งซื้อ";
};
