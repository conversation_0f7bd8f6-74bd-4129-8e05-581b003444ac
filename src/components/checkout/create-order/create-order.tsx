import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@base/button";
import {
  ContactInfo,
  ShippingAddress,
  TaxInvoice,
  ShippingMethod,
  PaymentMethod,
  TermsConditions,
} from "@/components/checkout";
import { checkoutSchema } from "@/pages/checkout/constants";
import type { CheckoutFormData } from "@/pages/checkout/constants";
import type { CreateOrderResponse } from "@/types/order";

interface CreateOrderProps {
  isAuthenticated: boolean;
  userEmail?: string;
  onOrderCreated: (order: CreateOrderResponse) => void;
  isCreatingOrder: boolean;
  orderError: string | null;
  onCreateOrder: (data: CheckoutFormData) => Promise<void>;
}

export const CreateOrder: React.FC<CreateOrderProps> = ({
  isAuthenticated,
  userEmail,
  onOrderCreated,
  isCreatingOrder,
  orderError,
  onCreateOrder,
}) => {
  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      contactInfo: {
        email: userEmail || "",
      },
      taxInvoice: {
        wantTaxInvoice: false,
        personalInfo: {
          type: "individual",
          firstName: "",
          lastName: "",
          email: userEmail || "",
          phone: "",
        },
      },
      agreeToTerms: false,
    },
  });

  React.useEffect(() => {
    if (userEmail) {
      setValue("contactInfo.email", userEmail);
      setValue("taxInvoice.personalInfo.email", userEmail);
    }
  }, [userEmail, setValue]);

  const onSubmit = async (data: CheckoutFormData) => {
    await onCreateOrder(data);
  };

  return (
    <div className="space-y-6">
      <h1 className="text-[32px] font-medium">เช็คเอ้าท์</h1>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <ContactInfo
          control={control}
          errors={errors}
          isAuthenticated={isAuthenticated}
        />

        <ShippingAddress
          control={control}
          errors={errors}
          setValue={setValue}
        />

        <TaxInvoice
          control={control}
          errors={errors}
          watch={watch}
          setValue={setValue}
        />

        <ShippingMethod control={control} errors={errors} />

        <PaymentMethod control={control} errors={errors} watch={watch} />

        <TermsConditions control={control} errors={errors} />

        {orderError && (
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-600">{orderError}</p>
          </div>
        )}

        <Button
          type="submit"
          size="lg"
          className="w-full"
          disabled={isSubmitting || isCreatingOrder}
        >
          {isCreatingOrder ? "กำลังสร้างคำสั่งซื้อ..." : isSubmitting ? "กำลังดำเนินการ..." : "สร้างคำสั่งซื้อ"}
        </Button>
      </form>
    </div>
  );
};
