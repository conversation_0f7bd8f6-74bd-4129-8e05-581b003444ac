import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@store/useAuthStore";
import { useOrder } from "@/hooks/useOrder";
import { ShoppingCart } from "@/components/shopping-cart";
import { CreateOrder, PaymentOrder } from "@/components/checkout";
import type { CheckoutFormData } from "./constants";
import type { Cart, CreateOrderResponse } from "@/types/order";

const CheckoutPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const navigate = useNavigate();
  const { isCreatingOrder, orderError, createOrderFromCheckout, clearOrderError } = useOrder();

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema),
    defaultValues: {
      contactInfo: {
        email: user?.email || "",
      },
      taxInvoice: {
        wantTaxInvoice: false,
        personalInfo: {
          type: "individual",
          firstName: "",
          lastName: "",
          email: user?.email || "",
          phone: "",
        },
      },
      agreeToTerms: false,
    },
  });

  useEffect(() => {
    if (user?.email) {
      setValue("contactInfo.email", user.email);
      setValue("taxInvoice.personalInfo.email", user.email);
    }
  }, [user, setValue]);

  const onSubmit = async (data: CheckoutFormData) => {
    try {
      clearOrderError();

      // Mock cart data - replace with actual cart from your cart store/context
      const mockCart: Cart = {
        id: 1,
        items: [
          {
            id: 1,
            product_variant_id: 123,
            quantity: 2,
            price: 1500,
            sku: "PROD-001"
          }
        ],
        total_price: 3000
      };

      // Extract payment method details from form data
      // You'll need to get these from your payment method component
      const paymentMethodString = data.paymentMethod;
      const cardProfileId = undefined; // Get from selected payment method
      const token = undefined; // Get from payment form if needed
      const deliveryAddressId = 0; // Get from selected address if using saved addresses

      console.log("Creating order with data:", data);

      const orderResponse = await createOrderFromCheckout(
        data,
        mockCart,
        data.shippingMethod,
        paymentMethodString,
        cardProfileId,
        token,
        deliveryAddressId
      );

      if (orderResponse) {
        console.log("Order created successfully:", orderResponse);
        navigate("/checkout/success", {
          state: { orderId: orderResponse.id }
        });
      }
    } catch (error) {
      console.error("Checkout error:", error);
    }
  };

  return (
    <div className="bg-bg-card py-5 pt-[140px]">
      <div className="mx-auto w-full px-8 pb-8">
        <h1 className="mb-8 text-[32px] font-medium">เช็คเอ้าท์</h1>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Contact Information */}
              <ContactInfo
                control={control}
                errors={errors}
                isAuthenticated={isAuthenticated}
              />

              {/* Shipping Address */}
              <ShippingAddress
                control={control}
                errors={errors}
                setValue={setValue}
              />

              {/* Tax Invoice */}
              <TaxInvoice
                control={control}
                errors={errors}
                watch={watch}
                setValue={setValue}
              />

              {/* Shipping Method */}
              <ShippingMethod control={control} errors={errors} />

              {/* Mobile: Shopping Cart at top */}
              <div className="lg:hidden">
                <ShoppingCart />
              </div>

              {/* Payment Method */}
              <PaymentMethod control={control} errors={errors} watch={watch} />

              {/* Terms and Conditions */}
              <TermsConditions control={control} errors={errors} />

              {/* Order Error Display */}
              {orderError && (
                <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                  <p className="text-sm text-red-600">{orderError}</p>
                </div>
              )}

              {/* Submit Button */}
              <Button
                type="submit"
                size="lg"
                className="w-full"
                disabled={isSubmitting || isCreatingOrder}
              >
                {isCreatingOrder ? "กำลังสร้างคำสั่งซื้อ..." : isSubmitting ? "กำลังดำเนินการ..." : "ชำระเงิน"}
              </Button>
            </form>
          </div>

          {/* Desktop: Shopping Cart Sidebar */}
          <div className="hidden lg:block">
            <ShoppingCart />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
