import { useState } from "react";
import type { PaymentStatus, PaymentState } from "./types";
import { PAYMENT_STATUS, PAYMENT_PROCESSING_DELAY, SUCCESS_REDIRECT_DELAY } from "./constants";

export const usePaymentProcessing = (onPaymentComplete: () => void) => {
  const [paymentState, setPaymentState] = useState<PaymentState>({
    isProcessingPayment: false,
    paymentStatus: PAYMENT_STATUS.PENDING as PaymentStatus,
  });

  const processPayment = async () => {
    setPaymentState(prev => ({
      ...prev,
      isProcessingPayment: true,
      paymentStatus: PAYMENT_STATUS.PENDING as PaymentStatus,
    }));

    try {
      await new Promise(resolve => setTimeout(resolve, PAYMENT_PROCESSING_DELAY));
      
      setPaymentState(prev => ({
        ...prev,
        paymentStatus: PAYMENT_STATUS.PAID as PaymentStatus,
      }));

      setTimeout(() => {
        onPaymentComplete();
      }, SUCCESS_REDIRECT_DELAY);
    } catch {
      setPaymentState(prev => ({
        ...prev,
        paymentStatus: PAYMENT_STATUS.FAILED as PaymentStatus,
      }));
    } finally {
      setPaymentState(prev => ({
        ...prev,
        isProcessingPayment: false,
      }));
    }
  };

  const resetPayment = () => {
    setPaymentState({
      isProcessingPayment: false,
      paymentStatus: PAYMENT_STATUS.PENDING as PaymentStatus,
    });
  };

  return {
    ...paymentState,
    processPayment,
    resetPayment,
  };
};
