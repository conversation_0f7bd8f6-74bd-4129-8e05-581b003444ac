import { CheckCircle, Clock, CreditCard } from "lucide-react";
import type { PaymentStatus } from "./types";

export const formatPrice = (price: string): string => {
  return new Intl.NumberFormat('th-TH', {
    style: 'currency',
    currency: 'THB',
  }).format(parseFloat(price));
};

export const getStatusIcon = (status: PaymentStatus) => {
  switch (status) {
    case "pending":
      return CreditCard;
    case "paid":
      return CheckCircle;
    case "failed":
      return CreditCard;
    case "refunded":
      return Clock;
    default:
      return CreditCard;
  }
};

export const getStatusText = (status: PaymentStatus): string => {
  switch (status) {
    case "pending":
      return "พร้อมชำระเงิน";
    case "paid":
      return "ชำระเงินสำเร็จ!";
    case "failed":
      return "การชำระเงินล้มเหลว";
    case "refunded":
      return "เงินถูกคืนแล้ว";
    default:
      return "พร้อมชำระเงิน";
  }
};

export const getStatusColor = (status: PaymentStatus): string => {
  switch (status) {
    case "pending":
      return "text-blue-500";
    case "paid":
      return "text-green-500";
    case "failed":
      return "text-red-500";
    case "refunded":
      return "text-yellow-500";
    default:
      return "text-blue-500";
  }
};

export const formatOrderDate = (dateString: string): string => {
  return new Date(dateString).toLocaleDateString('th-TH');
};
