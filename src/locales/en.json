{"navbar": {"home": "Home", "products": "Products", "login": "Login / Register", "myAccount": "My Account", "cart": "<PERSON><PERSON>", "overview": "Overview", "orders": "Orders", "favorites": "Favorites", "accountSettings": "Account <PERSON><PERSON>", "logout": "Logout"}, "home": {"title": "<PERSON><PERSON> Muse E-commerce", "subtitle": "A modern e-commerce platform built with Vite, React, Tailwind CSS, and shadcn/ui.", "register": "Register Now", "hero": {"title-1": "ช้อปสนุกกับ", "title-2": "ซีรีส์ที่คุณรัก!", "paragraph": "ฟินสุดขีดกับไอเทมหายากจากซีรีส์วายเรื่องโปรด ห้ามพลาด! ทุกชิ้นส่งตรงจาก Mojo Muse รับประกันความฟิน 300%"}, "recommend": {"title": "สินค้าแนะนำ", "all": "ดูสินค้าแนะนำทั้งหมด"}}, "products": {"title": "Products", "subtitle": "Browse our collection of products.", "filter": {"title": "ตัวกรอง", "category": "ศิลปิน", "clear": "ล้าง", "use-filter": "ใช้ตัวกรอง"}}, "product-detail": {"isInStock": "In Stock", "outOfStock": "Out of Stock", "size": "Size", "addToCart": "เพิ่มลงตะกร้า", "interest": "คุณอาจสนใจ"}, "login": {"title": "<PERSON><PERSON>", "email": "Email address", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "submit": "<PERSON><PERSON>", "submitting": "Logging in...", "success": "Login successful! Check the console for data.", "googleSuccess": "Google login successful!", "orContinueWith": "Or continue with", "continueWithGoogle": "Continue with Google", "noAccount": "Don't have an account?", "register": "Register"}, "register": {"title": "Register", "updateTitle": "We still require additional details!", "email": "Email address", "firstName": "First Name", "lastName": "Last Name", "password": "Password", "confirmPassword": "Confirm Password", "dateOfBirth": "Date of Birth", "mobileNumber": "Mobile Number", "submit": "Register", "submitting": "Registering...", "success": "Registration successful for {name}! Check the console for data.", "updateSuccess": "Profile updated successfully for {name}! Check the console for data.", "validation": {"phone": {"min": "Phone number must be at least 10 characters long", "max": "Phone number must be no more than 15 characters"}, "gender": {"required": "Gender is required", "max": "Gender must be no more than 50 characters"}}}, "notFound": {"title": "404 - Page Not Found", "message": "The page you are looking for does not exist.", "goHome": "Go Home"}, "myAccount": {"favoriteSettings": "Favorite Settings", "overviewTitle": "Account Overview", "overviewContent": "Your account overview information will be displayed here", "ordersTitle": "My Orders", "ordersContent": "Your order history will be displayed here", "favoritesTitle": "My Favorites", "favoritesContent": "Your favorite items will be displayed here", "favoriteSettingsTitle": "Favorite Settings", "favoriteSettingsContent": "Your favorite settings will be displayed here"}, "payment": {"methods": {"title": "Payment Methods", "current": "Current Payment Methods", "main": "Main Payment Method", "add": "Add Payment Method", "addCredit": "Add Credit/Debit Card", "edit": "Edit Payment Method", "create": "Add Payment Method", "cancel": "Cancel", "save": "Save", "addButton": "Add Card", "setDefault": "<PERSON>", "lastUsed": "Last used: {date}", "types": {"credit": "Credit Card", "debit": "Debit Card", "truemoney": "TrueMoney", "alipay": "AliPay", "promptpay": "PromptPay", "mobileBanking": "Mobile Banking"}, "form": {"type": "Payment Method Type", "cardBrand": "Card Brand", "cardNumber": "Card Number", "holderName": "Name on Card", "expiryDate": "Expiry Date", "expiryMonth": "Expiry Month", "expiryYear": "Expiry Year", "cvv": "CVV", "email": "Email", "account": "Account Information", "phoneNumber": "Phone Number", "displayName": "Display Name (Optional)", "isDefault": "Set as Default Payment Method", "placeholders": {"cardNumber": "1234 5678 9012 3456", "holderName": "<PERSON>", "expiryDate": "MM/YY", "expiryMonth": "MM", "expiryYear": "YYYY", "cvv": "123", "email": "<EMAIL>", "account": "Enter account information", "phoneNumber": "**********", "displayName": "My Card"}, "validation": {"cardNumberRequired": "Card number is required", "cardNumberFormat": "Card number must be 16 digits", "cardNumberInvalid": "Card number is invalid", "holderNameRequired": "Name on card is required", "expiryDateRequired": "Expiry date is required", "expiryDateFormat": "Expiry date must be in MM/YY format", "expiryMonthRequired": "Expiry month is required", "expiryYearRequired": "Expiry year is required", "expiryInvalid": "Card has expired", "cvvRequired": "CVV is required", "cvvFormat": "CVV must be 3-4 digits", "emailRequired": "Email is required", "emailFormat": "Invalid email format", "accountRequired": "Account information is required", "phoneNumberRequired": "Phone number is required", "phoneNumberFormat": "Phone number must be 10 digits", "phoneNumberInvalid": "Phone number is not registered with TrueMoney"}}, "altText": {"visa": "Visa", "mastercard": "Mastercard", "jcb": "JCB", "amex": "American Express", "truemoney": "TrueMoney", "alipay": "AliPay", "promptpay": "PromptPay", "mobileBanking": "Mobile Banking", "creditCard": "Credit Card"}, "creditCardDisplay": "Credit/Debit Card {brand} ending in {lastDigits}", "maskedCardNumber": "** {lastDigits}", "directOmise": "Direct Omise API Access", "empty": "No payment methods found", "delete": "Delete Payment Method", "deleteConfirmation": "Are you sure you want to delete this payment method?", "deleteSuccess": "Payment method deleted successfully", "deleteError": "Failed to delete payment method"}}, "address": {"title": "Shipping Address", "main": "Main Address", "deliveryInstructions": "Delivery Instructions", "edit": "Edit", "error": "Error loading addresses", "noAddresses": "No saved addresses", "addNew": "Add New Address", "form": {"addTitle": "Add New Address", "editTitle": "Edit Address", "recipientName": "Recipient Name", "recipientNamePlaceholder": "Enter recipient name", "phone": "Phone Number", "phonePlaceholder": "Enter phone number", "addressLine1": "Address Line 1", "addressLine1Placeholder": "House number, street, road", "addressLine2": "Address Line 2", "addressLine2Placeholder": "Building, floor, room (optional)", "subdistrict": "Subdistrict", "subdistrictPlaceholder": "Enter subdistrict", "district": "District", "districtPlaceholder": "Enter district", "city": "City", "cityPlaceholder": "Enter city", "province": "Province", "provincePlaceholder": "Enter province", "postalCode": "Postal Code", "postalCodePlaceholder": "Enter postal code", "country": "Country", "countryPlaceholder": "Enter country", "deliveryInstructions": "Delivery Instructions", "deliveryInstructionsPlaceholder": "Additional delivery instructions (optional)", "setAsDefault": "Set as default address", "cancel": "Cancel", "add": "Add Address", "update": "Update Address", "adding": "Adding...", "updating": "Updating..."}}, "taxInvoice": {"title": "Request Full Tax Invoice", "wantTaxInvoice": "Request tax invoice", "noTaxInvoice": "No tax invoice needed", "personalInfo": {"title": "Basic Information", "type": "Type", "typePlaceholder": "Select type", "individual": "Individual", "company": "Company", "firstName": "First Name", "lastName": "Last Name", "companyName": "Company Name", "email": "Email", "phone": "Phone Number"}, "taxInfo": {"title": "Tax Information", "taxId": "Tax ID Number", "taxIdIndividual": "National ID Number", "taxIdCompany": "Corporate Tax ID Number", "useSameAddress": "Use same address as shipping address", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "subdistrict": "Subdistrict", "district": "District", "city": "City", "province": "Province", "postalCode": "Postal Code", "country": "Country"}}, "shippingMethod": {"title": "Shipping Method"}}