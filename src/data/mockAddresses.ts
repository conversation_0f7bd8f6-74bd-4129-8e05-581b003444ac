import type { Address, AddressListResponse } from "@/types/address";

/**
 * Mock address data for development and testing
 */
export const mockAddresses: Address[] = [
  {
    id: 1,
    is_default: true,
    address_line1: "123/45 หมู่บ้านสวนสน",
    address_line2: "ซอยลาดพร้าว 15",
    sub_district: "จอมพล",
    district: "จตุจักร",
    city: "กรุงเทพมหานคร",
    province: "กรุงเทพมหานคร",
    postal_code: "10900",
    country: "ประเทศไทย",
    full_address:
      "123/45 หมู่บ้านสวนสน ซอยลาดพร้าว 15 จอมพล จตุจักร กรุงเทพมหานคร 10900",
    created_at: "2025-06-17T18:31:12.020Z",
    updated_at: "2025-06-17T18:31:12.020Z",
  },
  {
    id: 2,
    is_default: false,
    address_line1: "456/78 คอนโดมิเนียมเดอะไพรม์",
    address_line2: "ถนนสุขุมวิท",
    sub_district: "คลองตัน",
    district: "วัฒนา",
    city: "กรุงเทพมหานคร",
    province: "กรุงเทพมหานคร",
    postal_code: "10110",
    country: "ประเทศไทย",
    full_address:
      "456/78 คอนโดมิเนียมเดอะไพรม์ ถนนสุขุมวิท คลองตัน วัฒนา กรุงเทพมหานคร 10110",
    created_at: "2025-06-15T10:20:30.040Z",
    updated_at: "2025-06-16T14:25:45.050Z",
  },
  {
    id: 3,
    is_default: false,
    address_line1: "789/12 หมู่ 5",
    address_line2: "ตำบลบางพลี",
    sub_district: "บางพลี",
    district: "บางพลี",
    city: "สมุทรปราการ",
    province: "สมุทรปราการ",
    postal_code: "10540",
    country: "ประเทศไทย",
    full_address: "789/12 หมู่ 5 ตำบลบางพลี บางพลี บางพลี สมุทรปราการ 10540",
    created_at: "2025-06-10T08:15:20.030Z",
    updated_at: "2025-06-12T16:30:25.035Z",
  },
  {
    id: 4,
    is_default: false,
    address_line1: "321/54 ซอยรามคำแหง 24",
    address_line2: "แยก 3",
    sub_district: "หัวหมาก",
    district: "บางกะปิ",
    city: "กรุงเทพมหานคร",
    province: "กรุงเทพมหานคร",
    postal_code: "10240",
    country: "ประเทศไทย",
    full_address:
      "321/54 ซอยรามคำแหง 24 แยก 3 หัวหมาก บางกะปิ กรุงเทพมหานคร 10240",
    created_at: "2025-06-08T12:45:10.025Z",
    updated_at: "2025-06-14T09:20:15.028Z",
  },
  {
    id: 5,
    is_default: false,
    address_line1: "654/87 ถนนพหลโยธิน",
    address_line2: "ตรงข้ามเซ็นทรัลลาดพร้าว",
    sub_district: "สามเสนใน",
    district: "พญาไท",
    city: "กรุงเทพมหานคร",
    province: "กรุงเทพมหานคร",
    postal_code: "10400",
    country: "ประเทศไทย",
    full_address:
      "654/87 ถนนพหลโยธิน ตรงข้ามเซ็นทรัลลาดพร้าว สามเสนใน พญาไท กรุงเทพมหานคร 10400",
    created_at: "2025-06-05T15:30:40.020Z",
    updated_at: "2025-06-11T11:15:50.022Z",
  },
];

/**
 * Mock response for address list API
 */
export const mockAddressListResponse: AddressListResponse = {
  results: mockAddresses,
  count: mockAddresses.length,
  next: undefined,
  previous: undefined,
};

/**
 * Function to get mock addresses (simulates API call)
 */
export const getMockAddresses = async (params?: {
  page?: number;
  limit?: number;
}): Promise<AddressListResponse> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  const { page = 1, limit = 10 } = params || {};
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;

  const paginatedAddresses = mockAddresses.slice(startIndex, endIndex);

  return {
    results: paginatedAddresses,
    count: mockAddresses.length,
    next:
      endIndex < mockAddresses.length
        ? `?page=${page + 1}&limit=${limit}`
        : undefined,
    previous: page > 1 ? `?page=${page - 1}&limit=${limit}` : undefined,
  };
};

/**
 * Function to get a single mock address by ID
 */
export const getMockAddressById = async (
  id: number,
): Promise<Address | null> => {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 300));

  return mockAddresses.find((address) => address.id === id) || null;
};
