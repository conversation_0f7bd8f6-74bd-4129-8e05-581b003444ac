import { z } from "zod";

import {
  CREDIT_CARD_LENGTH_REGEX,
  CVV_REGEX,
  EXPIRY_DATE_VALIDATION_REGEX,
  EXPIRY_MONTH_REGEX,
  EXPIRY_YEAR_REGEX,
  PHONE_NUMBER_REGEX,
} from "@/constants/regex";

export const PaymentMethodType = {
  CREDIT_CARD: "credit_card",
  DEBIT_CARD: "debit_card",
  TRUEMONEY: "truemoney",
  ALIPAY: "alipay",
  PROMPTPAY: "promptpay",
  MOBILE_BANKING: "mobile_banking",
} as const;

export type PaymentMethodType =
  (typeof PaymentMethodType)[keyof typeof PaymentMethodType];

export interface BasePaymentMethod {
  id: string;
  type: PaymentMethodType;
  isDefault: boolean;
  isActive: boolean;
  displayName: string;
  createdAt: string;
  updatedAt: string;
  lastUsed?: string;
}

export interface CreditDebitCard extends BasePaymentMethod {
  type:
    | typeof PaymentMethodType.CREDIT_CARD
    | typeof PaymentMethodType.DEBIT_CARD;
  omiseToken: string;
  lastFourDigits: string;
  cardBrand: "visa" | "mastercard" | "jcb" | "amex";
  expiryMonth: string;
  expiryYear: string;
  holderName: string;
  bankName?: string;
}

export interface TrueMoneyWallet extends BasePaymentMethod {
  type: typeof PaymentMethodType.TRUEMONEY;
  phoneNumber: string;
  walletId?: string;
}

export interface AlipayAccount extends BasePaymentMethod {
  type: typeof PaymentMethodType.ALIPAY;
  alipayUserId?: string;
  accountEmail?: string;
}

export type PaymentMethod = CreditDebitCard | TrueMoneyWallet | AlipayAccount;

// API Response types
export interface PaymentMethodApiResponse {
  message: string;
  data: PaymentMethodApiData[];
}

export interface PaymentMethodApiData {
  id: string;
  last_four_digits: string;
  brand: string;
  expiry_month: number;
  expiry_year: number;
  name: string;
  is_default: boolean;
}

export interface CreditCardFormData {
  holderName: string;
  cardNumber: string;
  expiryMonth: string;
  expiryYear: string;
  expiryDate?: string; // New field for combined MM/YY format
  cvv: string;
  isDefault: boolean;
  displayName?: string;
  omiseToken: string;
}

export interface TrueMoneyFormData {
  phoneNumber: string;
  isDefault: boolean;
  displayName?: string;
}

export interface AlipayFormData {
  accountEmail?: string;
  isDefault: boolean;
  displayName?: string;
}

export const creditCardSchema = z.object({
  holderName: z
    .string()
    .min(2, "ชื่อผู้ถือบัตรต้องมีอย่างน้อย 2 ตัวอักษร")
    .max(50, "ชื่อผู้ถือบัตรต้องไม่เกิน 50 ตัวอักษร"),
  cardNumber: z
    .string()
    .regex(CREDIT_CARD_LENGTH_REGEX, "หมายเลขบัตรไม่ถูกต้อง")
    .optional(),
  expiryDate: z
    .string()
    .regex(EXPIRY_DATE_VALIDATION_REGEX, "รูปแบบวันหมดอายุไม่ถูกต้อง (MM/YY)")
    .optional(),
  expiryMonth: z
    .string()
    .regex(EXPIRY_MONTH_REGEX, "เดือนต้องอยู่ระหว่าง 01-12"),
  expiryYear: z.string().regex(EXPIRY_YEAR_REGEX, "ปีต้องเป็นตัวเลข 4 หลัก"),
  cvv: z.string().regex(CVV_REGEX, "CVV ต้องเป็นตัวเลข 3-4 หลัก").optional(),
  isDefault: z.boolean().default(false),
  displayName: z.string().optional(),
  omiseToken: z.string().optional(),
});

export const trueMoneySchema = z.object({
  phoneNumber: z.string().regex(PHONE_NUMBER_REGEX, "เบอร์โทรศัพท์ไม่ถูกต้อง"),
  isDefault: z.boolean().default(false),
  displayName: z.string().optional(),
});

export const alipaySchema = z.object({
  accountEmail: z.string().email("อีเมลไม่ถูกต้อง").optional(),
  isDefault: z.boolean().default(false),
  displayName: z.string().optional(),
});
