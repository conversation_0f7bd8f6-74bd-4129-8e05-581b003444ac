import React, { useState } from "react";
import { But<PERSON> } from "@base/button";
import { Badge } from "@commons/badge";
import { ArrowLeft, CreditCard, Clock, CheckCircle } from "lucide-react";
import type { CreateOrderResponse } from "@/types/order";

interface PaymentOrderProps {
  order: CreateOrderResponse;
  onBackToCreateOrder: () => void;
  onPaymentComplete: () => void;
}

export const PaymentOrder: React.FC<PaymentOrderProps> = ({
  order,
  onBackToCreateOrder,
  onPaymentComplete,
}) => {
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [paymentStatus, setPaymentStatus] = useState<"pending" | "processing" | "completed" | "failed">("pending");

  const handlePayment = async () => {
    setIsProcessingPayment(true);
    setPaymentStatus("processing");

    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      setPaymentStatus("completed");
      setTimeout(() => {
        onPaymentComplete();
      }, 1500);
    } catch (error) {
      setPaymentStatus("failed");
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const formatPrice = (price: string) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
    }).format(parseFloat(price));
  };

  const getStatusIcon = () => {
    switch (paymentStatus) {
      case "processing":
        return <Clock className="h-5 w-5 text-yellow-500 animate-spin" />;
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case "failed":
        return <CreditCard className="h-5 w-5 text-red-500" />;
      default:
        return <CreditCard className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusText = () => {
    switch (paymentStatus) {
      case "processing":
        return "กำลังดำเนินการชำระเงิน...";
      case "completed":
        return "ชำระเงินสำเร็จ!";
      case "failed":
        return "การชำระเงินล้มเหลว";
      default:
        return "พร้อมชำระเงิน";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={onBackToCreateOrder}
          disabled={isProcessingPayment || paymentStatus === "completed"}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          กลับไปแก้ไข
        </Button>
        <h1 className="text-[32px] font-medium">ชำระเงิน</h1>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="mb-4 flex items-center justify-between">
          <h2 className="text-lg font-semibold">คำสั่งซื้อ #{order.id}</h2>
          <Badge variant="outline" className="text-xs">
            สร้างเมื่อ {new Date(order.created_at).toLocaleDateString('th-TH')}
          </Badge>
        </div>

        <div className="space-y-4">
          <div className="flex justify-between items-center py-2 border-b">
            <span className="font-medium">ยอดรวม</span>
            <span className="text-lg font-semibold text-primary">
              {formatPrice(order.total_price)}
            </span>
          </div>

          <div className="space-y-2">
            <h3 className="font-medium text-sm text-gray-600">รายการสินค้า</h3>
            {order.items.map((item, index) => (
              <div key={index} className="flex justify-between items-center text-sm">
                <div>
                  <span className="font-medium">{item.sku}</span>
                  <span className="text-gray-500 ml-2">x{item.quantity}</span>
                </div>
                <span>{formatPrice(item.price)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="rounded-lg border bg-white p-6 shadow-sm">
        <div className="flex items-center gap-3 mb-4">
          {getStatusIcon()}
          <h3 className="text-lg font-semibold">{getStatusText()}</h3>
        </div>

        {paymentStatus === "pending" && (
          <div className="space-y-4">
            <p className="text-gray-600">
              กรุณาตรวจสอบข้อมูลคำสั่งซื้อและดำเนินการชำระเงิน
            </p>
            <Button
              onClick={handlePayment}
              size="lg"
              className="w-full"
              disabled={isProcessingPayment}
            >
              ชำระเงิน {formatPrice(order.total_price)}
            </Button>
          </div>
        )}

        {paymentStatus === "processing" && (
          <div className="text-center py-8">
            <div className="animate-pulse">
              <p className="text-gray-600">กำลังประมวลผลการชำระเงิน...</p>
              <p className="text-sm text-gray-500 mt-2">กรุณารอสักครู่</p>
            </div>
          </div>
        )}

        {paymentStatus === "completed" && (
          <div className="text-center py-8">
            <div className="text-green-600">
              <CheckCircle className="h-16 w-16 mx-auto mb-4" />
              <p className="text-lg font-semibold">การชำระเงินสำเร็จ!</p>
              <p className="text-sm text-gray-600 mt-2">
                คำสั่งซื้อของคุณได้รับการยืนยันแล้ว
              </p>
            </div>
          </div>
        )}

        {paymentStatus === "failed" && (
          <div className="space-y-4">
            <div className="text-center text-red-600">
              <p>การชำระเงินล้มเหลว กรุณาลองใหม่อีกครั้ง</p>
            </div>
            <Button
              onClick={handlePayment}
              size="lg"
              className="w-full"
              variant="outline"
            >
              ลองชำระเงินอีกครั้ง
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
